{
  "name": "Salesforce Data Retrieval for Invoice Generation",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "invoice-form",
        "options": {}
      },
      "id": "webhook-trigger",
      "name": "Webhook Trigger",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "invoice-form-trigger"
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.body.accountId}}",
              "operation": "isNotEmpty"
            }
          ]
        }
      },
      "id": "validate-input",
      "name": "Validate Input",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "resource": "query",
        "query": "SELECT Id, Name, BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountry, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, ShippingCountry, Phone, Website, Industry, Type, AccountNumber FROM Account WHERE Id = '{{$json.body.accountId}}' LIMIT 1"
      },
      "id": "get-account-data",
      "name": "Get Account Data",
      "type": "n8n-nodes-base.salesforce",
      "typeVersion": 1,
      "position": [680, 200],
      "credentials": {
        "salesforceApi": {
          "id": "salesforce-credentials",
          "name": "Salesforce API"
        }
      }
    },
    {
      "parameters": {
        "resource": "query",
        "query": "SELECT Id, FirstName, LastName, Name, Title, Email, Phone, MobilePhone, Department, MailingStreet, MailingCity, MailingState, MailingPostalCode, MailingCountry FROM Contact WHERE AccountId = '{{$json.body.accountId}}' AND IsActive = true ORDER BY LastName ASC, FirstName ASC LIMIT 50"
      },
      "id": "get-contacts",
      "name": "Get Contacts",
      "type": "n8n-nodes-base.salesforce",
      "typeVersion": 1,
      "position": [680, 320],
      "credentials": {
        "salesforceApi": {
          "id": "salesforce-credentials",
          "name": "Salesforce API"
        }
      }
    },
    {
      "parameters": {
        "resource": "query",
        "query": "SELECT Id, Name, Amount, CloseDate, StageName, Probability, Type, LeadSource, Description, Owner.Name, Owner.Email, Owner.Phone, CreatedDate, LastModifiedDate FROM Opportunity WHERE AccountId = '{{$json.body.accountId}}' AND IsClosed = false AND IsWon = false ORDER BY CloseDate ASC, Amount DESC LIMIT 25"
      },
      "id": "get-opportunities",
      "name": "Get Opportunities",
      "type": "n8n-nodes-base.salesforce",
      "typeVersion": 1,
      "position": [680, 440],
      "credentials": {
        "salesforceApi": {
          "id": "salesforce-credentials",
          "name": "Salesforce API"
        }
      }
    },
    {
      "parameters": {
        "resource": "query",
        "query": "SELECT Id, Name, SerialNumber, Product2.Name, Product2.ProductCode, Product2.Description, Status, PurchaseDate, InstallDate, UsageEndDate, Price, Quantity, Description FROM Asset WHERE AccountId = '{{$json.body.accountId}}' AND Status = 'Installed' ORDER BY InstallDate DESC LIMIT 20"
      },
      "id": "get-assets",
      "name": "Get Assets",
      "type": "n8n-nodes-base.salesforce",
      "typeVersion": 1,
      "position": [680, 560],
      "credentials": {
        "salesforceApi": {
          "id": "salesforce-credentials",
          "name": "Salesforce API"
        }
      }
    },
    {
      "parameters": {
        "mode": "combine",
        "combineBy": "combineAll",
        "options": {}
      },
      "id": "merge-data",
      "name": "Merge All Data",
      "type": "n8n-nodes-base.merge",
      "typeVersion": 2,
      "position": [900, 380]
    },
    {
      "parameters": {
        "jsCode": "// Process and structure the Salesforce data for the invoice form\nconst items = $input.all();\n\n// Initialize response structure\nlet response = {\n  success: true,\n  data: {\n    account: null,\n    contacts: [],\n    opportunities: [],\n    assets: []\n  },\n  formData: {\n    coName: '',\n    billingAddress: '',\n    shipToAddress: '',\n    defaultContact: null,\n    defaultOpportunity: null,\n    defaultAsset: null\n  }\n};\n\n// Process each data source\nfor (const item of items) {\n  const json = item.json;\n  \n  // Determine data source based on the structure\n  if (json.Id && json.Name && json.BillingStreet !== undefined) {\n    // Account data\n    response.data.account = json;\n    response.formData.coName = json.Name;\n    \n    // Build billing address\n    const billing = [\n      json.BillingStreet,\n      json.BillingCity,\n      json.BillingState,\n      json.BillingPostalCode,\n      json.BillingCountry\n    ].filter(Boolean).join(', ');\n    response.formData.billingAddress = billing;\n    \n    // Build shipping address (fallback to billing if empty)\n    const shipping = [\n      json.ShippingStreet || json.BillingStreet,\n      json.ShippingCity || json.BillingCity,\n      json.ShippingState || json.BillingState,\n      json.ShippingPostalCode || json.BillingPostalCode,\n      json.ShippingCountry || json.BillingCountry\n    ].filter(Boolean).join(', ');\n    response.formData.shipToAddress = shipping;\n    \n  } else if (json.Id && json.FirstName !== undefined) {\n    // Contact data\n    response.data.contacts.push(json);\n    \n    // Set default contact (first one alphabetically)\n    if (!response.formData.defaultContact) {\n      response.formData.defaultContact = {\n        id: json.Id,\n        name: json.Name,\n        title: json.Title,\n        email: json.Email\n      };\n    }\n    \n  } else if (json.Id && json.Amount !== undefined) {\n    // Opportunity data\n    response.data.opportunities.push(json);\n    \n    // Set default opportunity (nearest close date with highest amount)\n    if (!response.formData.defaultOpportunity || \n        new Date(json.CloseDate) < new Date(response.formData.defaultOpportunity.closeDate)) {\n      response.formData.defaultOpportunity = {\n        id: json.Id,\n        name: json.Name,\n        amount: json.Amount,\n        closeDate: json.CloseDate,\n        owner: json.Owner ? json.Owner.Name : '',\n        stageName: json.StageName\n      };\n    }\n    \n  } else if (json.Id && json.SerialNumber !== undefined) {\n    // Asset data\n    response.data.assets.push(json);\n    \n    // Set default asset (most recent install date)\n    if (!response.formData.defaultAsset || \n        new Date(json.InstallDate) > new Date(response.formData.defaultAsset.installDate)) {\n      response.formData.defaultAsset = {\n        id: json.Id,\n        name: json.Name,\n        serialNumber: json.SerialNumber,\n        productName: json.Product2 ? json.Product2.Name : json.Name,\n        installDate: json.InstallDate\n      };\n    }\n  }\n}\n\n// Set salesman from default opportunity\nif (response.formData.defaultOpportunity) {\n  response.formData.salesman = response.formData.defaultOpportunity.owner;\n  response.formData.price = response.formData.defaultOpportunity.amount;\n}\n\n// Generate reference number (can be customized)\nresponse.formData.refNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;\n\nreturn [response];"
      },
      "id": "process-data",
      "name": "Process Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 380]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{$json}}"
      },
      "id": "return-success",
      "name": "Return Success",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1340, 380]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "{\n  \"success\": false,\n  \"error\": \"Invalid or missing Account ID\",\n  \"message\": \"Please provide a valid Salesforce Account ID\"\n}",
        "options": {
          "responseCode": 400
        }
      },
      "id": "return-error",
      "name": "Return Error",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [680, 400]
    },
    {
      "parameters": {
        "jsCode": "// Error handling for Salesforce API failures\nconst error = $input.first().error;\nconst response = {\n  success: false,\n  error: \"Salesforce API Error\",\n  message: \"Unable to retrieve data from Salesforce. Please check the Account ID and try again.\",\n  details: error ? error.message : \"Unknown error occurred\"\n};\n\nreturn [response];"
      },
      "id": "handle-sf-error",
      "name": "Handle SF Error",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,\n      "position": [900, 500]\n    },\n    {\n      \"parameters\": {\n        \"respondWith\": \"json\",\n        \"responseBody\": \"={{$json}}\",\n        \"options\": {\n          \"responseCode\": 500\n        }\n      },\n      \"id\": \"return-sf-error\",\n      \"name\": \"Return SF Error\",\n      \"type\": \"n8n-nodes-base.respondToWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [1120, 500]\n    }\n  ],\n  \"connections\": {\n    \"Webhook Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Validate Input\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Validate Input\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Get Account Data\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"Get Contacts\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"Get Opportunities\",\n            \"type\": \"main\",\n            \"index\": 0\n          },\n          {\n            \"node\": \"Get Assets\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Return Error\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Account Data\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Merge All Data\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Get Contacts\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Merge All Data\",\n            \"type\": \"main\",\n            \"index\": 1\n          }\n        ]\n      ]\n    },\n    \"Get Opportunities\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Merge All Data\",\n            \"type\": \"main\",\n            \"index\": 2\n          }\n        ]\n      ]\n    },\n    \"Get Assets\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Merge All Data\",\n            \"type\": \"main\",\n            \"index\": 3\n          }\n        ]\n      ]\n    },\n    \"Merge All Data\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Process Data\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Process Data\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Return Success\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"active\": false,\n  \"settings\": {\n    \"timezone\": \"America/New_York\"\n  },\n  \"versionId\": \"1\",\n  \"meta\": {\n    \"templateCredsSetupCompleted\": true\n  },\n  \"id\": \"salesforce-data-retrieval\",\n  \"tags\": [\n    {\n      \"createdAt\": \"2025-06-17T20:51:00.000Z\",\n      \"updatedAt\": \"2025-06-17T20:51:00.000Z\",\n      \"id\": \"invoice-generation\",\n      \"name\": \"Invoice Generation\"\n    }\n  ]\n}
