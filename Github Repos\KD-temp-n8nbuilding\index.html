<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Generator</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: Inter, ui-sans-serif, system-ui, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
            min-height: 100vh;
            padding: 40px 20px;
            color: hsl(0, 0%, 98%);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
            line-height: inherit;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.12) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(29, 78, 216, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / .1),
                        0 8px 10px -6px rgb(0 0 0 / .1);
            overflow: hidden;
            position: relative;
            padding: 0;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            color: hsl(0, 0%, 98%);
            padding: 2rem;
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / .1),
                        0 8px 10px -6px rgb(0 0 0 / .1);
            position: relative;
            overflow: hidden;
            margin: 1rem;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: transparent;
            pointer-events: none;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .logo {
            width: 120px;
            height: 120px;
            margin-right: 30px;
            background: url('https://www.kdcapital.com/wp-content/uploads/2019/08/logo-white-1.png') no-repeat center;
            background-size: contain;
            filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
        }

        .company-info h1 {
            font-size: 1.8em;
            margin-bottom: 8px;
            font-weight: 700;
            background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .company-info p {
            font-size: 0.9em;
            opacity: 0.8;
            line-height: 1.5;
            font-weight: 400;
        }

        .header-right {
            text-align: right;
            position: relative;
        }

        .header-right h2 {
            font-size: 2.4em;
            font-weight: 200;
            letter-spacing: 4px;
            background: linear-gradient(135deg, #ffffff 0%, #cccccc 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-transform: uppercase;
        }
        
        .form-section {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 2rem;
            background-color: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / .1),
                        0 8px 10px -6px rgb(0 0 0 / .1);
            position: relative;
            margin: 1rem;
        }

        .step {
            margin-bottom: 40px;
            position: relative;
            animation: fade-in 0.6s ease-out 150ms;
            overflow: hidden;
            border-radius: 0.75rem;
            border-width: 1px;
            border-color: rgba(59, 130, 246, 0.2);
            background-image: linear-gradient(to bottom right, #0f172a, #1e293b, #0f172a);
            padding: 1.5rem;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                        border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                        box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            /* Set final animation state as default */
            opacity: 1;
            transform: translateY(0px) scale(1);
            transform-origin: center center;
        }

        .step::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
        }

        .step:hover {
            box-shadow: rgba(59, 130, 246, 0.09) 0px 15px 25px -15px,
                        rgba(59, 130, 246, 0.06) 0px 8px 12px -8px;
        }

        @keyframes fade-in {
            from {
                opacity: 0;
                transform: translateY(10px) scale(1);
            }
            to {
                opacity: 1;
                transform: translateY(0px) scale(1);
            }
        }

        .step h2 {
            color: hsl(0, 0%, 98%);
            margin-bottom: 24px;
            font-size: 1.5em;
            font-weight: 600;
            position: relative;
            padding-bottom: 12px;
        }

        .step h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
            border-radius: 2px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: hsl(0, 0%, 98%);
            font-size: 0.95em;
        }

        /* Keep loading and error text white */
        .loading,
        .error,
        .success {
            color: hsl(0, 0%, 98%);
        }

        .loading p {
            color: hsl(0, 0%, 98%);
        }

        input, select, textarea {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            font-family: 'Inter', sans-serif;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15),
                        0 8px 25px rgba(59, 130, 246, 0.2);
            background: rgba(255, 255, 255, 0.95);
            transform: scale(1.01);
        }

        input:hover, select:hover, textarea:hover {
            border-color: rgba(59, 130, 246, 0.4);
        }
        
        .btn {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
            color: white;
            padding: 18px 36px;
            border: none;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            font-family: 'Inter', sans-serif;
            text-transform: uppercase;
            letter-spacing: 0.8px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
                        0 0 40px rgba(59, 130, 246, 0.6);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .button-row {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
        }

        .button-row .btn {
            margin-right: auto;
        }

        .button-row .loading,
        .button-row .success-message {
            position: absolute;
            left: 65%;
            transform: translateX(-50%);
        }

        .success-message {
            display: none;
            align-items: center;
            gap: 8px;
            color: #10b981;
            font-weight: 500;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .success-message.show {
            display: flex;
            opacity: 1;
        }

        .success-message.fade-out {
            opacity: 0;
        }

        .success-message i {
            font-size: 1.1em;
        }

        .field-check {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
        }

        .check-indicator {
            width: 24px;
            height: 24px;
            border: 2px solid #ef4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
            background: rgba(0, 0, 0, 0.2);
        }

        .check-indicator:hover {
            border-color: rgba(59, 130, 246, 0.6);
            background: rgba(59, 130, 246, 0.1);
            transform: scale(1.1);
        }

        .check-indicator.checked {
            border-color: #10b981;
            background: #10b981;
            color: white;
        }

        .check-indicator.checked:hover {
            background: #059669;
            border-color: #059669;
        }

        /* Green border for checked fields */
        .field-check.verified input,
        .field-check.verified select,
        .field-check.verified textarea {
            border-color: #10b981 !important;
            border-width: 3px !important;
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3) !important;
        }

        .check-indicator i {
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .check-indicator.checked i {
            opacity: 1;
        }
        
        .loading {
            display: none;
            align-items: center;
            gap: 10px;
            color: hsl(0, 0%, 98%);
            font-weight: 500;
        }

        .loading.show {
            display: flex;
        }
        
        .spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            flex-shrink: 0;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #fdf2f2;
            border: 1px solid #e74c3c;
            color: #c0392b;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }

        .success {
            background: #f0f9f0;
            border: 1px solid #27ae60;
            color: #1e8449;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .hidden {
            display: none;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        @media (max-width: 600px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .header-left {
                margin-bottom: 20px;
            }

            .logo {
                margin: 0 auto 15px;
            }
        }

        /* Invoice-style table for better organization */
        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .detail-section h3 {
            color: #222426;
            font-size: 1.1em;
            margin-bottom: 10px;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 18px;
            border: 1px solid #d5dbdb;
            border-radius: 6px;
            background: white;
            transition: all 0.3s ease;
        }

        .terms-checkbox:hover {
            border-color: #222426;
            box-shadow: 0 2px 8px rgba(34, 36, 38, 0.1);
        }

        .terms-checkbox input[type="checkbox"] {
            width: auto;
            margin-right: 12px;
            margin-top: 3px;
            transform: scale(1.2);
        }

        .terms-checkbox-content {
            flex: 1;
        }

        .terms-checkbox-title {
            font-weight: 600;
            color: #222426;
            margin-bottom: 8px;
            font-size: 1.05em;
        }

        .terms-checkbox-text {
            font-size: 14px;
            color: #5d6d7e;
            line-height: 1.5;
        }

        .terms-toggle {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .toggle-btn {
            padding: 10px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(0, 0, 0, 0.2);
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .toggle-btn:hover {
            border-color: rgba(59, 130, 246, 0.6);
            background: rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .toggle-btn.active {
            border-color: #3b82f6;
            background: #3b82f6;
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .terms-section {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <div class="logo"></div>
                <div class="company-info">
                    <h1>KD CAPITAL EQUIPMENT</h1>
                    <p>7918 E McClain Drive – Suite 101 – Scottsdale, AZ 85260<br>
                    Tel: (************* | Fax: (*************</p>
                </div>
            </div>
            <div class="header-right">
                <h2>INVOICE<br>DRAFTER</h2>
            </div>
        </div>
        
        <div class="form-section">
            <!-- Step 1: Account ID Input -->
            <div class="step" id="step1">
                <h2>Step 1: Load Account Data</h2>
                <div class="form-group">
                    <label for="accountId">Account ID (from Salesforce):</label>
                    <input type="text" id="accountId" placeholder="e.g., A-1774170" />
                </div>

                <div class="button-row">
                    <button class="btn" onclick="loadAccountData()">Load Account Data</button>

                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>Loading Salesforce data...</p>
                    </div>

                    <div class="success-message" id="success-message">
                        <i class="fas fa-check-circle"></i>
                        <span>Account data loaded successfully!</span>
                    </div>
                </div>

                <div id="error-message"></div>
            </div>
            
            <!-- Step 2: Invoice Form (Hidden initially) -->
            <div class="step hidden" id="step2">
                <h2>Step 2: Invoice Details</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="companyName">Company Name:</label>
                        <div class="field-check">
                            <input type="text" id="companyName" readonly />
                            <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="salesman">Salesman:</label>
                        <div class="field-check">
                            <input type="text" id="salesman" value="Chris Loy" />
                            <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="billingAddress">Billing Address:</label>
                    <div class="field-check">
                        <textarea id="billingAddress" rows="3" readonly></textarea>
                        <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="shipToAddress">Ship To Address:</label>
                    <div class="field-check">
                        <textarea id="shipToAddress" rows="3"></textarea>
                        <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="contactSelect">Contact:</label>
                        <div class="field-check">
                            <select id="contactSelect">
                                <option value="">Select Contact...</option>
                            </select>
                            <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="refSelect">Reference Number:</label>
                        <div class="field-check">
                            <select id="refSelect">
                                <option value="">Select Reference...</option>
                            </select>
                            <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price">Price ($):</label>
                        <div class="field-check">
                            <input type="text" id="price" placeholder="Enter price manually" onblur="formatPrice(this)" />
                            <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="invoiceDate">Invoice Date:</label>
                        <div class="field-check">
                            <input type="date" id="invoiceDate" />
                            <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Terms (select applicable):</label>

                    <!-- Terms Type Toggle -->
                    <div class="terms-toggle" style="margin-bottom: 15px;">
                        <button type="button" class="toggle-btn active" onclick="showTermsType('inv')" id="invToggle">
                            📄 Invoice Terms
                        </button>
                        <button type="button" class="toggle-btn" onclick="showTermsType('po')" id="poToggle">
                            📋 PO Terms
                        </button>
                    </div>

                    <!-- Invoice Terms -->
                    <div id="invTermsCheckboxes" class="terms-section">
                        <!-- INV terms will be populated here -->
                    </div>

                    <!-- PO Terms -->
                    <div id="poTermsCheckboxes" class="terms-section" style="display: none;">
                        <!-- PO terms will be populated here -->
                    </div>

                    <label for="termsText">Terms Text (editable):</label>
                    <div class="field-check">
                        <textarea id="termsText" rows="6" placeholder="Selected terms will appear here and can be edited..."></textarea>
                        <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="notes">Notes/Description:</label>
                    <div class="field-check">
                        <textarea id="notes" rows="4" placeholder="Additional notes or item description..."></textarea>
                        <div class="check-indicator" onclick="toggleCheck(this)" title="Mark as verified">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn" onclick="generateInvoice()">Generate Invoice</button>
                </div>
                
                <div id="invoice-result"></div>
            </div>
        </div>
    </div>



    <script>
        // Configuration - Your n8n webhook URL
        const N8N_WEBHOOK_URL = 'https://oakhill007.app.n8n.cloud/webhook/invoice-form';

        let accountData = null;

        // Hard-coded terms data
        const TERMS_DATA = {
            inv: [
                {
                    field: 'invoice_standard',
                    label: 'Standard Invoice Terms',
                    content: 'Machinery will not be released until we receive completed sales tax exemption certificates, if applicable, or sales tax is paid.'
                },

                {
                    field: 'transportation_contribution',
                    label: 'Transportation Contribution',
                    content: 'K.D. Capital Equipment, LLC agrees to give $x,xxx.xx towards transportation of the machine on behalf of Buyer. Buyer is responsible for offloading costs.'
                },
                {
                    field: 'additional_trucking_charges',
                    label: 'Additional Trucking Charges',
                    content: 'Buyer agrees to pay any additional charges for trucking or rigging, due to trucking scheduling problems or delays.'
                },
                {
                    field: 'buyer_decommissioning',
                    label: 'Buyer Decommissioning',
                    content: 'Buyer is responsible for decommissioning and loading the machine.'
                },
                {
                    field: 'deposit_10_percent',
                    label: '10% Refundable Deposit',
                    content: '10% refundable deposit due now.'
                },
                {
                    field: 'deposit_25_percent',
                    label: '25% Deposit',
                    content: '25% deposit due now.'
                },
                {
                    field: 'pending_inspection',
                    label: 'Pending Inspection',
                    content: 'Machine sold pending buyer\'s inspection.'
                },
                {
                    field: 'buyer_loading_cost',
                    label: 'Buyer Loading Cost',
                    content: 'Buyer to cover the cost of loading the machine onto the truck.'
                },
                {
                    field: 'as_is_warranty',
                    label: 'As-Is Sale',
                    content: 'Machine is sold as-is, where-is, with no other warranties or guarantees expressed or implied.'
                },
                {
                    field: 'payment_full_acceptance',
                    label: 'Payment Upon Acceptance',
                    content: 'Payment in full upon acceptance.'
                },
                {
                    field: 'removal_schedule',
                    label: 'Removal Schedule',
                    content: 'Machine will be loaded out approximately Month Day, Year. Removal date is predicated on new machine arrival. Firm date still to be determined.'
                },
                {
                    field: 'permission_transport',
                    label: 'Permission-Based Transport',
                    content: '*WITH PERMISSION ONLY* KD Capital Equipment, LLC agrees to pay for loading and transportation of the machine on behalf of Buyer. Buyer is responsible for offloading costs.'
                },
                {
                    field: 'sold_as_inspected',
                    label: 'Sold As Inspected',
                    content: 'Machine sold as inspected by Buyer.'
                },
                {
                    field: 'terms_supersede',
                    label: 'Terms Supersede',
                    content: 'Terms from KD Capital Equipment\'s invoice to supersede all other terms.'
                },
                {
                    field: 'sale_confirmation',
                    label: 'Sale Terms Confirmation',
                    content: 'Please confirm acceptance of Invoice #[Invoice Number] for the referenced equipment. You declined inspection and hereby accept the equipment AS IS, WHERE IS, with all faults and no warranties. You waive claims for condition, repairs, or missing parts and release KD Capital Equipment, LLC from all liability. Full terms, including disclaimers, risk of loss, and legal jurisdiction, are in the invoice.'
                }
            ],
            po: [
                {
                    field: 'po_prep_load',
                    label: 'Prep & Load',
                    content: '__________ to lock, block, drain fluids, clean, prepare, and load machines on behalf of Buyer\'s Customer.'
                },
                {
                    field: 'po_inspection',
                    label: 'Inspection & Financing',
                    content: 'Machines purchased subject to inspection, approval, and financing by Buyer\'s customer.'
                },
                {
                    field: 'po_photos',
                    label: 'Photo Documentation',
                    content: 'Seller agrees to send digital photos of machines decommissioned and prep to KD Capital Equipment, LLC, at least 24 hours prior to removal. Seller agrees to document and send day of load out digital photos of machines prep prior to loading, after loaded on truck, and after secured and tarped on truck.'
                },
                {
                    field: 'po_lien_warranty',
                    label: 'Lien Warranty',
                    content: 'Seller warrants machine to be free and clear of all liens and encumbrances.'
                },
                {
                    field: 'po_seller_loading',
                    label: 'Seller Loading Responsibility',
                    content: 'KD Capital Equipment, LLC\'s Seller to load machine onto truck on behalf of Buyer.'
                },
                {
                    field: 'po_seller_loading_alt',
                    label: 'Seller Loading (Alternative)',
                    content: 'Seller to load machine onto truck on behalf of Buyer.'
                }
            ]
        };

        // Set today's date as default
        document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

        // Initialize terms on page load
        document.addEventListener('DOMContentLoaded', function() {
            populateTerms();
        });

        function populateTerms() {
            // Populate INV terms
            const invContainer = document.getElementById('invTermsCheckboxes');
            invContainer.innerHTML = '';

            TERMS_DATA.inv.forEach((term, index) => {
                const checkboxDiv = document.createElement('div');
                checkboxDiv.className = 'terms-checkbox';

                checkboxDiv.innerHTML = `
                    <input type="checkbox" id="inv_term_${index}" value="${term.field}" onchange="updateTermsText()">
                    <div class="terms-checkbox-content">
                        <div class="terms-checkbox-title">${term.label}</div>
                        <div class="terms-checkbox-text">${term.content}</div>
                    </div>
                `;

                invContainer.appendChild(checkboxDiv);
            });

            // Populate PO terms
            const poContainer = document.getElementById('poTermsCheckboxes');
            poContainer.innerHTML = '';

            TERMS_DATA.po.forEach((term, index) => {
                const checkboxDiv = document.createElement('div');
                checkboxDiv.className = 'terms-checkbox';

                checkboxDiv.innerHTML = `
                    <input type="checkbox" id="po_term_${index}" value="${term.field}" onchange="updateTermsText()">
                    <div class="terms-checkbox-content">
                        <div class="terms-checkbox-title">${term.label}</div>
                        <div class="terms-checkbox-text">${term.content}</div>
                    </div>
                `;

                poContainer.appendChild(checkboxDiv);
            });
        }

        function showTermsType(type) {
            // Update toggle buttons
            document.getElementById('invToggle').classList.toggle('active', type === 'inv');
            document.getElementById('poToggle').classList.toggle('active', type === 'po');

            // Show/hide term sections
            document.getElementById('invTermsCheckboxes').style.display = type === 'inv' ? 'block' : 'none';
            document.getElementById('poTermsCheckboxes').style.display = type === 'po' ? 'block' : 'none';
        }

        async function loadAccountData() {
            const accountId = document.getElementById('accountId').value.trim();

            if (!accountId) {
                showError('Please enter an Account ID');
                return;
            }

            // Show loading
            document.getElementById('loading').classList.add('show');
            document.getElementById('error-message').innerHTML = '';

            try {
                const response = await fetch(N8N_WEBHOOK_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ accountId: accountId })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                accountData = data;

                // Populate form with loaded data
                populateForm(data);

                // Show step 2
                document.getElementById('step2').classList.remove('hidden');

                // Hide loading
                document.getElementById('loading').classList.remove('show');

                showSuccess('Account data loaded successfully!');

            } catch (error) {
                console.error('Error loading account data:', error);
                showError('Failed to load account data. Please check the Account ID and try again.');
                document.getElementById('loading').classList.remove('show');
            }
        }

        function populateForm(data) {
            const defaults = data.defaults;

            // Populate basic fields
            document.getElementById('companyName').value = defaults.coName || '';
            document.getElementById('billingAddress').value = defaults.billingAddress || '';
            document.getElementById('shipToAddress').value = defaults.shipToAddress || '';

            // Terms are now hard-coded and populated on page load

            // Populate contact dropdown
            const contactSelect = document.getElementById('contactSelect');
            contactSelect.innerHTML = '<option value="">Select Contact...</option>';

            if (defaults.contactOptions) {
                defaults.contactOptions.forEach(contact => {
                    const option = document.createElement('option');
                    option.value = contact.value;
                    option.textContent = contact.label;
                    option.dataset.email = contact.email || '';
                    option.dataset.phone = contact.phone || '';
                    contactSelect.appendChild(option);
                });

                // Set default contact if available
                if (defaults.defaultContact) {
                    contactSelect.value = defaults.defaultContact.value;
                }
            }

            // Populate reference dropdown
            const refSelect = document.getElementById('refSelect');
            refSelect.innerHTML = '<option value="">Select Reference...</option>';

            if (defaults.refOptions) {
                defaults.refOptions.forEach(ref => {
                    const option = document.createElement('option');
                    option.value = ref.value;
                    option.textContent = ref.label;
                    option.dataset.price = ref.price || '';
                    option.dataset.refNumber = ref.refNumber || '';
                    option.dataset.machineTitle = ref.machineTitle || '';
                    refSelect.appendChild(option);
                });

                // Set default reference if available
                if (defaults.defaultRef) {
                    refSelect.value = defaults.defaultRef.value;
                    // Note: Price is NOT auto-filled - sales rep enters manually
                }
            }

            // Note: Removed price auto-population - sales rep enters price manually
        }

        async function generateInvoice() {
            try {
                // Show loading state
                const generateBtn = document.querySelector('button[onclick="generateInvoice()"]');
                const originalText = generateBtn.textContent;
                generateBtn.textContent = 'Generating Invoice...';
                generateBtn.disabled = true;

                // Collect form data
                const invoiceData = {
                    accountId: document.getElementById('accountId').value,
                    companyName: document.getElementById('companyName').value,
                    salesman: document.getElementById('salesman').value,
                    billingAddress: document.getElementById('billingAddress').value,
                    shipToAddress: document.getElementById('shipToAddress').value,
                    contact: getSelectedContactData(),
                    reference: getSelectedReferenceData(),
                    terms: getSelectedTermsData(),
                    price: getPriceValue(),
                    invoiceDate: document.getElementById('invoiceDate').value,
                    notes: document.getElementById('notes').value,
                    originalData: accountData,
                    // Email configuration
                    emailTo: '<EMAIL>',
                    emailSubject: `Invoice Draft - ${document.getElementById('companyName').value}`,
                    emailMessage: 'Please review the attached invoice draft and forward to Taylor if approved.'
                };

                console.log('Sending invoice data to n8n:', invoiceData);

                // Send to n8n webhook
                const response = await fetch(N8N_WEBHOOK_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(invoiceData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('n8n response:', result);

                // Reset button
                generateBtn.textContent = originalText;
                generateBtn.disabled = false;

                showSuccess('Invoice generated and <NAME_EMAIL> for review!');

            } catch (error) {
                console.error('Error generating invoice:', error);

                // Reset button
                const generateBtn = document.querySelector('button[onclick="generateInvoice()"]');
                generateBtn.textContent = 'Generate Invoice';
                generateBtn.disabled = false;

                showError('Failed to generate invoice. Please try again or contact support.');
            }
        }

        function getSelectedContactData() {
            const select = document.getElementById('contactSelect');
            const option = select.options[select.selectedIndex];
            return {
                id: select.value,
                name: option.textContent,
                email: option.dataset.email,
                phone: option.dataset.phone
            };
        }

        function getSelectedReferenceData() {
            const select = document.getElementById('refSelect');
            const option = select.options[select.selectedIndex];
            return {
                id: select.value,
                label: option.textContent,
                price: option.dataset.price,
                refNumber: option.dataset.refNumber,
                machineTitle: option.dataset.machineTitle
            };
        }

        function updateTermsText() {
            const invCheckboxes = document.querySelectorAll('#invTermsCheckboxes input[type="checkbox"]:checked');
            const poCheckboxes = document.querySelectorAll('#poTermsCheckboxes input[type="checkbox"]:checked');
            const termsTextArea = document.getElementById('termsText');

            let combinedTerms = [];

            // Add INV terms
            invCheckboxes.forEach(checkbox => {
                const termDiv = checkbox.closest('.terms-checkbox');
                const termText = termDiv.querySelector('.terms-checkbox-text').textContent;
                combinedTerms.push(termText);
            });

            // Add PO terms
            poCheckboxes.forEach(checkbox => {
                const termDiv = checkbox.closest('.terms-checkbox');
                const termText = termDiv.querySelector('.terms-checkbox-text').textContent;
                combinedTerms.push(termText);
            });

            termsTextArea.value = combinedTerms.join('\n\n');
        }

        function getSelectedTermsData() {
            const invCheckboxes = document.querySelectorAll('#invTermsCheckboxes input[type="checkbox"]:checked');
            const poCheckboxes = document.querySelectorAll('#poTermsCheckboxes input[type="checkbox"]:checked');
            const selectedTerms = [];

            // Collect INV terms
            invCheckboxes.forEach(checkbox => {
                const termDiv = checkbox.closest('.terms-checkbox');
                const title = termDiv.querySelector('.terms-checkbox-title').textContent;
                const content = termDiv.querySelector('.terms-checkbox-text').textContent;

                selectedTerms.push({
                    field: checkbox.value,
                    label: `INV - ${title}`,
                    content: content,
                    type: 'inv'
                });
            });

            // Collect PO terms
            poCheckboxes.forEach(checkbox => {
                const termDiv = checkbox.closest('.terms-checkbox');
                const title = termDiv.querySelector('.terms-checkbox-title').textContent;
                const content = termDiv.querySelector('.terms-checkbox-text').textContent;

                selectedTerms.push({
                    field: checkbox.value,
                    label: `PO - ${title}`,
                    content: content,
                    type: 'po'
                });
            });

            return {
                selectedTerms: selectedTerms,
                finalText: document.getElementById('termsText').value
            };
        }

        function showError(message) {
            document.getElementById('error-message').innerHTML =
                `<div class="error">${message}</div>`;
        }

        function showSuccess(message) {
            const successElement = document.getElementById('success-message');
            const span = successElement.querySelector('span');
            span.textContent = message;

            // Show with fade in
            successElement.classList.add('show');

            // Auto fade out after 3 seconds
            setTimeout(() => {
                successElement.classList.add('fade-out');
                setTimeout(() => {
                    successElement.classList.remove('show', 'fade-out');
                }, 300);
            }, 3000);
        }

        function toggleCheck(indicator) {
            indicator.classList.toggle('checked');

            // Toggle green border on the field container
            const fieldContainer = indicator.closest('.field-check');
            fieldContainer.classList.toggle('verified');

            // Add a little bounce effect
            indicator.style.transform = 'scale(1.2)';
            setTimeout(() => {
                indicator.style.transform = '';
            }, 150);
        }

        function formatPrice(input) {
            let value = input.value;

            // Remove any existing formatting
            value = value.replace(/[$,]/g, '');

            // Parse as number
            const numValue = parseFloat(value);

            // If it's a valid number, format it
            if (!isNaN(numValue) && numValue >= 0) {
                // Format as currency: $48,000.00
                const formatted = new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(numValue);

                input.value = formatted;
            }
        }

        function getPriceValue() {
            const priceInput = document.getElementById('price');
            let value = priceInput.value;

            // Remove formatting and return numeric value
            value = value.replace(/[$,]/g, '');
            const numValue = parseFloat(value);

            return isNaN(numValue) ? 0 : numValue;
        }
    </script>
</body>
</html>
