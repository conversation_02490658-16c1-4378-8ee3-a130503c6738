# Salesforce SOQL Queries Documentation

## 📊 Overview

This document outlines the SOQL queries used to retrieve data from Salesforce for invoice generation. All queries are designed for read-only access and optimized for performance.

## 🏢 Account Queries

### Primary Account Query
Retrieves basic company information and billing address.

```sql
SELECT 
    Id, 
    Name, 
    BillingStreet, 
    BillingCity, 
    BillingState, 
    BillingPostalCode, 
    BillingCountry,
    ShippingStreet,
    ShippingCity,
    ShippingState,
    ShippingPostalCode,
    ShippingCountry,
    Phone,
    Website,
    Industry,
    Type,
    AccountNumber
FROM Account 
WHERE Id = '{accountId}'
   OR Name LIKE '%{accountName}%'
LIMIT 1
```

**Usage**: Primary query when user provides Account ID or searches by name.

**Response Fields**:
- `Id`: Salesforce Account ID
- `Name`: Company name for invoice header
- `Billing*`: Primary billing address fields
- `Shipping*`: Alternative shipping address (if different)
- `Phone`, `Website`: Additional contact information
- `Industry`, `Type`: Business classification
- `AccountNumber`: External account reference

### Account Search Query
For typeahead/search functionality when user doesn't know exact Account ID.

```sql
SELECT Id, Name, BillingCity, BillingState, Type
FROM Account 
WHERE Name LIKE '%{searchTerm}%'
   OR AccountNumber LIKE '%{searchTerm}%'
ORDER BY Name ASC
LIMIT 10
```

## 👥 Contact Queries

### Account Contacts Query
Retrieves all contacts associated with the account for dropdown selection.

```sql
SELECT 
    Id,
    FirstName,
    LastName,
    Name,
    Title,
    Email,
    Phone,
    MobilePhone,
    Department,
    MailingStreet,
    MailingCity,
    MailingState,
    MailingPostalCode,
    MailingCountry
FROM Contact 
WHERE AccountId = '{accountId}'
  AND IsActive = true
ORDER BY LastName ASC, FirstName ASC
LIMIT 50
```

**Usage**: Populates the "Attention" dropdown in the invoice form.

**Response Processing**:
- Display format: `{FirstName} {LastName} - {Title}`
- Default selection: First contact in alphabetical order
- Include email for invoice delivery options

### Primary Contact Query
Gets the primary contact when a specific contact is selected.

```sql
SELECT 
    Id,
    Name,
    Title,
    Email,
    Phone,
    Department,
    MailingAddress
FROM Contact 
WHERE Id = '{contactId}'
LIMIT 1
```

## 💼 Opportunity Queries

### Account Opportunities Query
Retrieves deals/opportunities for the account to populate deal dropdown.

```sql
SELECT 
    Id,
    Name,
    Amount,
    CloseDate,
    StageName,
    Probability,
    Type,
    LeadSource,
    Description,
    Owner.Name,
    Owner.Email,
    Owner.Phone,
    CreatedDate,
    LastModifiedDate
FROM Opportunity 
WHERE AccountId = '{accountId}'
  AND IsClosed = false
  AND IsWon = false
ORDER BY CloseDate ASC, Amount DESC
LIMIT 25
```

**Usage**: Populates the "Deal" dropdown with active opportunities.

**Response Processing**:
- Display format: `{Name} - ${Amount} ({StageName})`
- Default selection: Nearest close date with highest amount
- Owner information used for "Salesman" field

### Closed Won Opportunities
For reference and historical invoice generation.

```sql
SELECT 
    Id,
    Name,
    Amount,
    CloseDate,
    Owner.Name,
    Description
FROM Opportunity 
WHERE AccountId = '{accountId}'
  AND IsWon = true
ORDER BY CloseDate DESC
LIMIT 10
```

## 🏭 Asset Queries

### Account Assets Query
Retrieves machines, equipment, or products associated with the account.

```sql
SELECT 
    Id,
    Name,
    SerialNumber,
    Product2.Name,
    Product2.ProductCode,
    Product2.Description,
    Status,
    PurchaseDate,
    InstallDate,
    UsageEndDate,
    Price,
    Quantity,
    Description
FROM Asset 
WHERE AccountId = '{accountId}'
  AND Status = 'Installed'
ORDER BY InstallDate DESC
LIMIT 20
```

**Usage**: Optional section for machine/equipment information on invoices.

**Response Processing**:
- Display format: `{Product2.Name} - {SerialNumber}`
- Include in invoice as line items or reference information
- Price information for service billing

## 🔍 Query Optimization Guidelines

### Performance Best Practices
1. **Field Selection**: Only query fields that are actually used
2. **WHERE Clauses**: Always include specific filters to limit results
3. **LIMIT Clauses**: Prevent large result sets that could timeout
4. **Indexing**: Use indexed fields (Id, Name, AccountId) in WHERE clauses
5. **Relationship Queries**: Minimize nested relationship queries

### Error Handling
```sql
-- Example with error handling consideration
SELECT Id, Name, BillingStreet 
FROM Account 
WHERE Id = '{accountId}'
  AND IsDeleted = false
LIMIT 1
```

### Query Validation
Before executing queries, validate:
- Account ID format (18-character Salesforce ID)
- Special character escaping in search terms
- Date format validation for date filters
- Null value handling in response processing

## 📋 Data Mapping for Invoice Form

### Form Field Mappings
| Invoice Form Field | Salesforce Source | Query | Default Logic |
|-------------------|-------------------|-------|---------------|
| CO Name | Account.Name | Account Query | Direct mapping |
| Billing Address | Account.Billing* | Account Query | Concatenated address |
| Ship-To Address | Account.Shipping* | Account Query | Falls back to Billing |
| Attention | Contact.Name | Contact Query | First alphabetical |
| Deal | Opportunity.Name | Opportunity Query | Nearest close date |
| Machine Info | Asset.Name | Asset Query | Most recent install |
| Salesman | Opportunity.Owner.Name | Opportunity Query | From selected deal |
| REF # | Custom Logic | - | Generated or manual |
| Price | Opportunity.Amount | Opportunity Query | From selected deal |

### Response Data Structure
```json
{
  "account": {
    "id": "001XXXXXXXXXXXXXXX",
    "name": "Acme Corporation",
    "billingAddress": {
      "street": "123 Main St",
      "city": "Anytown",
      "state": "CA",
      "postalCode": "12345",
      "country": "USA"
    }
  },
  "contacts": [
    {
      "id": "003XXXXXXXXXXXXXXX",
      "name": "John Doe",
      "title": "Purchasing Manager",
      "email": "<EMAIL>"
    }
  ],
  "opportunities": [
    {
      "id": "006XXXXXXXXXXXXXXX",
      "name": "Q4 Equipment Purchase",
      "amount": 50000,
      "owner": "Jane Smith"
    }
  ],
  "assets": [
    {
      "id": "02iXXXXXXXXXXXXXXXX",
      "name": "CNC Machine Model X",
      "serialNumber": "SN123456"
    }
  ]
}
```

## 🔒 Security Considerations

### Query Security
- **Parameterized Queries**: Always use parameter binding to prevent SOQL injection
- **Field-Level Security**: Respect Salesforce field-level security settings
- **Object Permissions**: Verify read access to all queried objects
- **Row-Level Security**: Honor sharing rules and record access

### Data Privacy
- **Minimal Data**: Only query fields necessary for invoice generation
- **Temporary Storage**: Don't persist sensitive data beyond processing
- **Audit Trail**: Log all data access for compliance
- **Encryption**: Ensure data transmission is encrypted (HTTPS)

## 🧪 Testing Queries

### Test Data Requirements
```sql
-- Create test account
INSERT INTO Account (Name, BillingStreet, BillingCity, BillingState) 
VALUES ('Test Invoice Company', '123 Test St', 'Test City', 'CA');

-- Create test contact
INSERT INTO Contact (FirstName, LastName, AccountId, Email) 
VALUES ('Test', 'Contact', '{accountId}', '<EMAIL>');

-- Create test opportunity
INSERT INTO Opportunity (Name, AccountId, Amount, CloseDate, StageName) 
VALUES ('Test Deal', '{accountId}', 10000, '2025-12-31', 'Proposal');
```

### Query Performance Testing
- Test with large datasets (1000+ records)
- Measure query execution time
- Validate timeout handling
- Test with various data scenarios (missing fields, null values)

---

*This documentation should be updated whenever SOQL queries are modified or new queries are added.*
