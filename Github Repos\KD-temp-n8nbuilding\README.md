# External Invoice Generation Tool

🚀 **Transform your Salesforce data into professional invoices without modifying your Salesforce org!**

This tool provides a seamless, external solution for generating invoices by pulling data from Salesforce via SOQL queries, presenting an editable form interface, and outputting professional PDF invoices or emails.

## ✨ Features

- 📊 **Salesforce Integration**: Read-only SOQL access to pull Account, Contact, Opportunity, and Asset data
- 📝 **Smart Form Interface**: Auto-populated form with dropdown overrides for Contact and Deal selection
- 🎨 **Professional Output**: Generate branded PDF invoices or send via email
- 🔒 **Secure & External**: No Salesforce modifications required, secure webhook endpoints
- ⚡ **n8n Powered**: Visual workflow automation for easy maintenance and modifications
- 📱 **Responsive Design**: Mobile-friendly interface for on-the-go invoice creation

## 🏗️ Architecture

```
User Input → Salesforce SOQL → Form Generation → User Review → Invoice Output
     ↓              ↓               ↓              ↓            ↓
Account ID → Data Retrieval → Auto-fill Form → Override Fields → PDF/Email
```

### Technology Stack
- **Backend**: n8n workflows for automation and orchestration
- **Frontend**: HTML/CSS/JavaScript for form interface
- **Data Source**: Salesforce via SOQL queries
- **Output**: HTML to PDF conversion, SMTP email integration
- **Security**: API token authentication, input validation

## 📋 Prerequisites

- Salesforce org with SOQL query access
- n8n instance (cloud or self-hosted)
- SMTP server for email functionality (optional)
- Basic understanding of n8n workflows

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd KD-temp-n8nbuilding
```

### 2. Set Up n8n Environment
```bash
# Install n8n (if not already installed)
npm install -g n8n

# Start n8n
n8n start
```

### 3. Import n8n Workflows
1. Open n8n interface (typically http://localhost:5678)
2. Import the workflow files from `/n8n-workflows/`:
   - `invoice-form-trigger.json` - Handles form display and data retrieval
   - `invoice-submit-process.json` - Processes form submissions and generates output
   - `salesforce-data-retrieval.json` - Manages Salesforce SOQL queries

### 4. Configure Salesforce Connection
1. In n8n, set up Salesforce credentials:
   - Username/Password or OAuth2
   - Instance URL
   - API Version (recommended: v58.0 or latest)

2. Test the connection with a sample SOQL query:
   ```sql
   SELECT Id, Name FROM Account LIMIT 1
   ```

### 5. Configure Webhooks
1. Set up webhook endpoints in n8n:
   - `/api/v1/invoice-form` - Form trigger endpoint
   - `/api/v1/invoice-submit` - Form submission endpoint

2. Update webhook URLs in the HTML templates

### 6. Customize Invoice Template
1. Edit `/templates/invoice-template.html` with your branding
2. Modify `/templates/styles/invoice.css` for styling
3. Update company logo and contact information

## 📖 Usage

### Basic Invoice Generation
1. Navigate to the invoice form URL (provided by n8n webhook)
2. Enter a Salesforce Account ID or Name
3. Review auto-populated data (Company, Contacts, Deals)
4. Override any fields using the dropdown menus
5. Add invoice-specific details (REF #, Price, etc.)
6. Click "Generate Invoice" to create PDF or send email

### Form Fields
- **CO Name**: Company name from Salesforce Account
- **Billing Address**: Auto-filled from Account billing address
- **Ship-To Address**: Defaults to billing, can be edited
- **Attention (Contact)**: Dropdown of Account contacts
- **Deal**: Dropdown of Account opportunities
- **Machine Info**: Optional asset/product information
- **REF #**: Reference number for tracking
- **Price**: Invoice amount (can override from Opportunity)
- **Salesman**: Auto-filled from Opportunity owner

## 🔧 Configuration

### Environment Variables
Create a `.env` file in your n8n environment:
```env
SALESFORCE_USERNAME=your-sf-username
SALESFORCE_PASSWORD=your-sf-password
SALESFORCE_SECURITY_TOKEN=your-sf-token
SALESFORCE_INSTANCE_URL=https://your-instance.salesforce.com
SMTP_HOST=your-smtp-server.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
```

### Webhook Security
1. Generate API tokens for webhook authentication
2. Configure IP whitelisting if needed
3. Enable HTTPS for production deployment

## 🧪 Testing

### Unit Tests
```bash
# Run individual workflow tests
npm test -- --testPathPattern=unit
```

### Integration Tests
```bash
# Test complete invoice generation flow
npm test -- --testPathPattern=integration
```

### Manual Testing Checklist
- [ ] Form loads with Account data
- [ ] Dropdowns populate correctly
- [ ] Form validation works
- [ ] PDF generation produces clean output
- [ ] Email delivery functions properly
- [ ] Error handling displays user-friendly messages

## 📚 Documentation

- [API Documentation](docs/api/README.md) - Webhook endpoints and parameters
- [User Guide](docs/user-guide/README.md) - Step-by-step usage instructions
- [Deployment Guide](docs/deployment-guide.md) - Production deployment instructions
- [Security Guide](security/webhook-security.md) - Security best practices

## 🔒 Security

- **Authentication**: API token-based webhook security
- **Input Validation**: All form inputs are sanitized
- **Data Protection**: No sensitive data is stored permanently
- **Audit Logging**: All actions are logged for compliance
- **HTTPS Only**: Secure transmission of all data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs or request features via GitHub Issues
- **Community**: Join our discussions for tips and best practices

## 🗺️ Roadmap

- [ ] **v1.0**: Basic invoice generation with Salesforce integration
- [ ] **v1.1**: Multi-currency support and tax calculations
- [ ] **v1.2**: Approval workflows and email notifications
- [ ] **v1.3**: Advanced templates and branding options
- [ ] **v2.0**: API access and third-party integrations

---

**Built with ❤️ using n8n and Salesforce**

*For questions or support, please refer to the documentation or create an issue.*
