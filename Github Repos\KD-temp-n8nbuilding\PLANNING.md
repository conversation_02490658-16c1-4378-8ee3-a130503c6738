# External Invoice Generation Tool - Project Planning

## 🎯 Project Overview

**Goal**: Build an external invoice generation tool that pulls Salesforce data via SOQL, auto-fills a form, allows overrides (via dropdown picklists), and generates a final output (PDF or email).

**Context**: 
- User has read-only SOQL access to Salesforce
- No modifications to Salesforce (no new Flows, Apex, or Lightning components)
- Using n8n for automation and backend orchestration
- Output: Professional invoice PDF or email with editable review step

## 🏗️ Architecture & Design Patterns

### Technology Stack
- **Backend Orchestration**: n8n workflows
- **Frontend**: HTML forms with JavaScript for dynamic interactions
- **Data Source**: Salesforce via SOQL queries
- **Output Generation**: HTML to PDF conversion, Email integration
- **Security**: API tokens, input sanitization, webhook security

### File Structure
```
/
├── PLANNING.md              # This file - project architecture and guidelines
├── TASK.md                  # Task tracking and progress
├── BUSINESSPLAN.md          # High-level business overview and scaling strategy
├── README.md                # Setup instructions and usage guide
├── n8n-workflows/           # n8n workflow JSON exports
│   ├── invoice-form-trigger.json
│   ├── invoice-submit-process.json
│   └── salesforce-data-retrieval.json
├── templates/               # HTML templates and assets
│   ├── invoice-form.html
│   ├── invoice-template.html
│   └── styles/
├── docs/                    # Documentation
│   ├── api-documentation.md
│   ├── deployment-guide.md
│   └── user-guide.md
├── tests/                   # Test files
│   ├── unit/
│   └── integration/
└── security/                # Security configurations
    └── webhook-security.md
```

### Naming Conventions
- **Files**: kebab-case (e.g., `invoice-form.html`)
- **Variables**: camelCase in JavaScript, snake_case in n8n
- **Functions**: camelCase with descriptive names
- **Webhooks**: `/api/v1/invoice-[action]` format

### Code Organization Principles
- **Modularity**: Each n8n workflow handles a specific responsibility
- **Separation of Concerns**: Form logic, data processing, and output generation are separate
- **Reusability**: Common functions and templates are shared across workflows
- **Error Handling**: Comprehensive error handling at each stage
- **Logging**: Detailed logging for debugging and audit trails

## 🔄 Data Flow Architecture

### 1. Form Trigger Flow
```
User Input (Account ID) → Salesforce SOQL Queries → Data Processing → Form Generation → User Display
```

### 2. Invoice Generation Flow
```
Form Submission → Data Validation → Template Population → PDF Generation → Email/Download
```

### 3. Salesforce Data Queries
- **Account**: Basic company information and billing address
- **Contact**: Available contacts for the account
- **Opportunity**: Deal information and amounts
- **Asset**: Machine/product information (optional)

## 🎨 UI/UX Design Guidelines

### Form Design Principles
- **Progressive Disclosure**: Show relevant fields based on data availability
- **Smart Defaults**: Pre-fill with most likely values
- **Clear Hierarchy**: Group related fields logically
- **Responsive Design**: Mobile-friendly interface
- **Accessibility**: WCAG 2.1 AA compliance

### Form Sections
1. **Company Information**: Auto-filled from Account
2. **Contact Selection**: Dropdown with available contacts
3. **Deal Information**: Dropdown with opportunities
4. **Machine/Asset Details**: Optional section
5. **Invoice Details**: Editable pricing and reference fields
6. **Review & Submit**: Final confirmation step

## 🔒 Security Considerations

### Authentication & Authorization
- API token-based authentication for webhooks
- IP whitelisting for internal use
- Input validation and sanitization
- Rate limiting on endpoints

### Data Protection
- No sensitive data storage (process and discard)
- Secure transmission (HTTPS only)
- Audit logging for compliance
- Error messages that don't expose system details

## 📊 Performance & Scalability

### Optimization Strategies
- Efficient SOQL queries with field selection
- Caching of frequently accessed data
- Asynchronous processing for heavy operations
- Minimal payload sizes

### Monitoring & Logging
- n8n execution logs
- Custom error tracking
- Performance metrics
- User activity logs

## 🧪 Testing Strategy

### Test Coverage
- Unit tests for individual n8n nodes
- Integration tests for complete workflows
- End-to-end tests for user scenarios
- Security penetration testing

### Test Data
- Mock Salesforce responses
- Sample form submissions
- Edge case scenarios
- Error condition testing

## 🚀 Deployment & Maintenance

### Environment Setup
- Development: Local n8n instance
- Staging: Cloud n8n with test data
- Production: Secure cloud deployment

### Version Control
- n8n workflows exported as JSON
- Template files in version control
- Documentation updates with each release
- Change log maintenance

## 📈 Success Metrics

### Functional Requirements
- ✅ User can input Account ID and get accurate pre-filled form
- ✅ User can override dropdown selections (Contact, Deal, etc.)
- ✅ System generates professional PDF or sends email
- ✅ No Salesforce admin intervention required
- ✅ System modifications possible through n8n interface

### Performance Targets
- Form load time: < 3 seconds
- PDF generation: < 5 seconds
- Email delivery: < 10 seconds
- 99.9% uptime availability

## 🔧 Development Guidelines

### Code Quality Standards
- Comprehensive inline comments explaining business logic
- Error handling for all external API calls
- Input validation at every entry point
- Consistent formatting and naming conventions

### Documentation Requirements
- README with setup instructions
- API documentation for all webhooks
- User guide with screenshots
- Troubleshooting guide

### Review Process
- Code review for all n8n workflow changes
- Testing validation before deployment
- Security review for new endpoints
- Documentation updates with feature changes
